import type { Schema, Attribute } from '@strapi/strapi';

export interface SharedUrlGroup extends Schema.Component {
  collectionName: 'components_shared_url_groups';
  info: {
    displayName: 'URL Group';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    url: Attribute.Component<'base.url'> &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: false;
        };
      }>;
    additionalUrls: Attribute.Component<'base.url', true> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: false;
        };
      }>;
    includeInSitemap: Attribute.Boolean &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<false>;
  };
}

export interface SharedServiceButton extends Schema.Component {
  collectionName: 'components_shared_service_buttons';
  info: {
    displayName: 'serviceButton';
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    caption: Attribute.String &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    service: Attribute.Enumeration<['Remittance', 'MobileTopUp', 'GiftCard']> &
      Attribute.Required &
      Attribute.DefaultTo<'Remittance'>;
  };
}

export interface SharedSeo extends Schema.Component {
  collectionName: 'components_shared_seos';
  info: {
    displayName: 'seo';
    icon: 'book';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
      customi18n: {
        localized: false;
      };
    };
  };
  attributes: {
    metaTitle: Attribute.String &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 70;
      }>;
    metaDescription: Attribute.Text &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 250;
      }>;
    metaSocial: Attribute.Component<'shared.meta-social', true> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    keywords: Attribute.Text &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface SharedPopoverPageAction extends Schema.Component {
  collectionName: 'components_shared_popover-page-action';
  info: {
    displayName: 'Select changes to be commited';
    icon: 'dashboard';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    customDynamicZoneField: Attribute.String &
      Attribute.CustomField<'plugin::strapi-plugin-ui-fields.popover-page-action'>;
  };
}

export interface SharedNavbarCountrySelector extends Schema.Component {
  collectionName: 'components_shared_navbar_country_selectors';
  info: {
    displayName: 'navbarCountrySelector';
    icon: 'filter';
  };
  attributes: {
    hasCountries: Attribute.Boolean & Attribute.DefaultTo<false>;
    dialogTitle: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      >;
  };
}

export interface SharedMetaSocial extends Schema.Component {
  collectionName: 'components_shared_meta_socials';
  info: {
    displayName: 'metaSocial';
    icon: 'project-diagram';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
      customi18n: {
        localized: false;
      };
    };
  };
  attributes: {
    socialNetwork: Attribute.Enumeration<
      ['Facebook', 'Twitter', 'Youtube', 'Instagram']
    > &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    title: Attribute.String &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 60;
      }>;
    description: Attribute.String &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 65;
      }>;
    image: Attribute.Media<'images' | 'files' | 'videos'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface SharedManageLayoutModal extends Schema.Component {
  collectionName: 'components_shared_manage-layout-modal';
  info: {
    displayName: 'Layout changes';
    icon: 'dashboard';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    customDynamicZoneField: Attribute.String &
      Attribute.CustomField<'plugin::strapi-plugin-ui-fields.manage-layout-modal'>;
  };
}

export interface SharedLocation extends Schema.Component {
  collectionName: 'components_shared_locations';
  info: {
    displayName: 'Location';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
      customi18n: {
        localized: false;
      };
    };
  };
  attributes: {
    street: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    city: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    country: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    state: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    findUs: Attribute.Text &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface SharedEventScheduler extends Schema.Component {
  collectionName: 'components_shared_event_schedulers';
  info: {
    displayName: 'Event Scheduler';
    icon: 'calendar';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
      customi18n: {
        localized: false;
      };
    };
  };
  attributes: {
    eventStart: Attribute.DateTime &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    eventEnd: Attribute.DateTime &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    allDay: Attribute.Boolean &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<false>;
    timezone: Attribute.String &
      Attribute.CustomField<'plugin::timezone-select.timezone'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface SharedDiscountCode extends Schema.Component {
  collectionName: 'components_shared_discount_codes';
  info: {
    displayName: 'discountCode';
    icon: 'expand';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    title: Attribute.String;
    shortDescription: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    fullDescription: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface SharedDeleteServiceType extends Schema.Component {
  collectionName: 'components_shared_delete-service-type';
  info: {
    displayName: 'Delete changes';
    icon: 'dashboard';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
      customi18n: {
        localized: true;
      };
    };
  };
  attributes: {
    customDynamicZoneField: Attribute.String &
      Attribute.CustomField<'plugin::strapi-plugin-ui-fields.delete-service-type'>;
  };
}

export interface SharedCustomDynamicZone extends Schema.Component {
  collectionName: 'components_shared_custom_dynamic_zones';
  info: {
    displayName: 'Custom Dynamic Zone';
    icon: 'dashboard';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    customDynamicZoneField: Attribute.String &
      Attribute.CustomField<'plugin::strapi-plugin-ui-fields.custom-dynamic-zone'>;
  };
}

export interface SharedContactInformation extends Schema.Component {
  collectionName: 'components_shared_contact_informations';
  info: {
    displayName: 'Contact Information';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
      customi18n: {
        localized: false;
      };
    };
  };
  attributes: {
    email: Attribute.Email &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    website: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    contactName: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    cellPhone: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    phone: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
  };
}

export interface SharedCaptionByPage extends Schema.Component {
  collectionName: 'components_shared_caption_by_page';
  info: {
    displayName: 'Caption By Page';
    description: '';
  };
  attributes: {
    page: Attribute.Relation<
      'shared.caption-by-page',
      'oneToOne',
      'api::page.page'
    > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    alt: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    caption: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface MetaSocialMedia extends Schema.Component {
  collectionName: 'components_meta_social_medias';
  info: {
    displayName: 'Social Media';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    title: Attribute.String &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 90;
      }>;
    image: Attribute.Media<'images'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    url: Attribute.Component<'base.url'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: false;
        };
      }>;
    aditionalUrls: Attribute.Component<'base.url', true> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    includeSitemap: Attribute.Boolean &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<false>;
  };
}

export interface MetaMetadata extends Schema.Component {
  collectionName: 'components_meta_metadata';
  info: {
    displayName: 'Metadata';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    metaTitle: Attribute.String &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 60;
      }>;
    metaDescription: Attribute.Text &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 155;
      }>;
  };
}

export interface SectionsTextList extends Schema.Component {
  collectionName: 'components_sections_text_lists';
  info: {
    displayName: 'TextsList';
    description: 'A rich text list';
    icon: 'server';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'TextList'>;
    texts: Attribute.Component<'base.rich-text', true> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface SectionsShopList extends Schema.Component {
  collectionName: 'components_sections_shop_lists';
  info: {
    displayName: 'ShopsList';
    icon: 'chartBubble';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'Shops'>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    action: Attribute.Component<'base.action'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    shopsCount: Attribute.Integer & Attribute.DefaultTo<3>;
    shops: Attribute.Relation<
      'sections.shop-list',
      'oneToMany',
      'api::shop.shop'
    >;
  };
}

export interface SectionsSendEmailForm extends Schema.Component {
  collectionName: 'components_sections_send_email_forms';
  info: {
    displayName: 'SendEmailForm';
    icon: 'envelop';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String & Attribute.DefaultTo<'Send Email'>;
    email: Attribute.Component<'base.input'> & Attribute.Required;
    button: Attribute.Component<'base.action'> &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface SectionsReview extends Schema.Component {
  collectionName: 'components_sections_reviews';
  info: {
    displayName: 'Reviews';
    description: 'Section to show the fake reviews';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'Reviews'>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    image: Attribute.Media<'images'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    action: Attribute.Component<'base.action'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    imageCaption: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    stats: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    type: Attribute.Enumeration<['fake', 'real']>;
    reviews: Attribute.JSON & Attribute.DefaultTo<[]>;
  };
}

export interface SectionsPromotionsList extends Schema.Component {
  collectionName: 'components_sections_promotions_lists';
  info: {
    displayName: 'PromotionsList';
    icon: 'layer';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'Promotions'>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    promosCount: Attribute.Integer;
    promoShowedCount: Attribute.Integer;
    whichShow: Attribute.Enumeration<['Most recents', 'Selected']> &
      Attribute.DefaultTo<'Most recents'>;
    style: Attribute.Enumeration<['static', 'carrousel']> &
      Attribute.DefaultTo<'static'>;
    showImage: Attribute.Boolean & Attribute.DefaultTo<true>;
    showSummary: Attribute.Boolean & Attribute.DefaultTo<true>;
    showCattergory: Attribute.Boolean & Attribute.DefaultTo<true>;
    promotions: Attribute.Relation<
      'sections.promotions-list',
      'oneToMany',
      'api::promotion.promotion'
    >;
    moreDetailButtonText: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
        customi18n: {
          localized: true;
        };
      }>;
    action: Attribute.Component<'base.action'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    variant: Attribute.Relation<
      'sections.promotions-list',
      'oneToOne',
      'api::variants.variant'
    > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
        customi18n: {
          localized: false;
        };
      }>;
  };
}

export interface SectionsPromotion extends Schema.Component {
  collectionName: 'components_sections_promotions';
  info: {
    displayName: 'Promotion';
    icon: 'handHeart';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'Promotion'>;
    promotion: Attribute.Relation<
      'sections.promotion',
      'oneToOne',
      'api::promotion.promotion'
    > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface SectionsPartnersList extends Schema.Component {
  collectionName: 'components_sections_partners_lists';
  info: {
    displayName: 'PartnersList';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'Partners'>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    partners: Attribute.JSON;
  };
}

export interface SectionsOperatorsList extends Schema.Component {
  collectionName: 'components_sections_operators_lists';
  info: {
    displayName: 'OperatorsList';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
      customi18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'Operators'>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    action: Attribute.Component<'base.action'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    telcoms: Attribute.Relation<
      'sections.operators-list',
      'oneToMany',
      'api::telcom.telcom'
    >;
    telcomsCount: Attribute.Integer & Attribute.DefaultTo<3>;
  };
}

export interface SectionsNotificationBanner extends Schema.Component {
  collectionName: 'components_sections_notification_banners';
  info: {
    displayName: 'NotificationBanner';
    icon: 'bell';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
      customi18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'NotificationBanner'>;
    type: Attribute.Enumeration<['info', 'warning', 'alert']> &
      Attribute.DefaultTo<'info'>;
    icon: Attribute.Media<'images'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    text: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    action: Attribute.Component<'base.action'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
  };
}

export interface SectionsNewsList extends Schema.Component {
  collectionName: 'components_sections_news_lists';
  info: {
    displayName: 'NewsList';
    icon: 'stack';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
      customi18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'PostList'>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    moreDetailButtonText: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
        customi18n: {
          localized: true;
        };
      }>;
    newsCount: Attribute.Integer & Attribute.DefaultTo<3>;
    newsShowedCount: Attribute.Integer & Attribute.DefaultTo<3>;
    whichShow: Attribute.Enumeration<['Most recents', 'Selected']> &
      Attribute.DefaultTo<'Most recents'>;
    style: Attribute.Enumeration<['static', 'carrousel']> &
      Attribute.DefaultTo<'static'>;
    showImage: Attribute.Boolean & Attribute.DefaultTo<true>;
    showSummary: Attribute.Boolean & Attribute.DefaultTo<true>;
    showCattegory: Attribute.Boolean & Attribute.DefaultTo<true>;
    news: Attribute.Relation<
      'sections.news-list',
      'oneToMany',
      'api::news-post.news-post'
    > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    action: Attribute.Component<'base.action'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
  };
}

export interface SectionsLogoList extends Schema.Component {
  collectionName: 'components_sections_logo_lists';
  info: {
    displayName: 'LogosList';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'Logo List'>;
    logos: Attribute.Component<'base.logo', true> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface SectionsJobOffersList extends Schema.Component {
  collectionName: 'components_sections_job_offers_lists';
  info: {
    displayName: 'JobOffersList';
    icon: 'archive';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
      customi18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'Offers'>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    offers: Attribute.Relation<
      'sections.job-offers-list',
      'oneToMany',
      'api::job-offer.job-offer'
    > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    offertDetailsText: Attribute.Text &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface SectionsJobOffer extends Schema.Component {
  collectionName: 'components_sections_job_offers';
  info: {
    displayName: 'JobOffer';
    icon: 'archive';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
      customi18n: {
        localized: false;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'Offer'>;
    jobOffer: Attribute.Relation<
      'sections.job-offer',
      'oneToOne',
      'api::job-offer.job-offer'
    > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    skillsAtributeText: Attribute.String;
    responsibilitiesAtributeText: Attribute.String;
  };
}

export interface SectionsHero extends Schema.Component {
  collectionName: 'components_sections_heroes';
  info: {
    displayName: 'Hero';
    icon: 'landscape';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'Hero'>;
    image: Attribute.Media<'images'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: false;
        };
      }>;
    calculator: Attribute.Enumeration<
      ['None', 'Remittance', 'MobileTopUp', 'GiftCard']
    > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'None'>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    actions: Attribute.Component<'base.action', true> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: false;
        };
      }>;
    variant: Attribute.Relation<
      'sections.hero',
      'oneToOne',
      'api::variants.variant'
    > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
        customi18n: {
          localized: false;
        };
      }>;
  };
}

export interface SectionsHeading extends Schema.Component {
  collectionName: 'components_base_headings';
  info: {
    displayName: 'Heading';
    description: 'Component used to represent a title and its description';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'Heading'>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    variant: Attribute.Relation<
      'sections.heading',
      'oneToOne',
      'api::variants.variant'
    > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
        customi18n: {
          localized: false;
        };
      }>;
  };
}

export interface SectionsHeadingList extends Schema.Component {
  collectionName: 'components_sections_heading_lists';
  info: {
    displayName: 'HeadingsList';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'Heading List'>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    headings: Attribute.Component<'sections.heading', true> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    variant: Attribute.Relation<
      'sections.heading-list',
      'oneToOne',
      'api::variants.variant'
    > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
        customi18n: {
          localized: false;
        };
      }>;
  };
}

export interface SectionsFeatureList extends Schema.Component {
  collectionName: 'components_sections_feature_lists';
  info: {
    displayName: 'FeaturesList';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'Feature List'>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    features: Attribute.Component<'base.feature', true> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    variant: Attribute.Relation<
      'sections.feature-list',
      'oneToOne',
      'api::variants.variant'
    > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
        customi18n: {
          localized: false;
        };
      }>;
  };
}

export interface SectionsFeatListAcordion extends Schema.Component {
  collectionName: 'components_sections_feat_list_acordions';
  info: {
    displayName: 'FeatListAcordion';
    icon: 'layer';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    titleledFeatureLists: Attribute.Component<
      'base.titleled-feature-list',
      true
    > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    video: Attribute.Component<'sections.cta'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    countryTitle: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    countryDescription: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    countryAction: Attribute.Component<'base.action'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    countries: Attribute.Relation<
      'sections.feat-list-acordion',
      'oneToMany',
      'api::core-country.core-country'
    > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: false;
        };
      }>;
  };
}

export interface SectionsFaqList extends Schema.Component {
  collectionName: 'components_sections_faq_lists';
  info: {
    displayName: 'FAQsList';
    icon: 'question';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
      customi18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'FAQs'>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    faqs: Attribute.Relation<'sections.faq-list', 'oneToMany', 'api::faq.faq'>;
    action: Attribute.Component<'base.action'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface SectionsFaqByCategories extends Schema.Component {
  collectionName: 'components_sections_faq_by_categories';
  info: {
    displayName: 'FAQByCategories';
    icon: 'dashboard';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'FAQsByCategories'>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    faqByCategories: Attribute.JSON;
    action: Attribute.Component<'base.action'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    input: Attribute.Component<'base.input'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    noResultText: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    variant: Attribute.Relation<
      'sections.faq-by-categories',
      'oneToOne',
      'api::variants.variant'
    > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
        customi18n: {
          localized: false;
        };
      }>;
  };
}

export interface SectionsFakeMsg extends Schema.Component {
  collectionName: 'components_sections_fake_msgs';
  info: {
    displayName: 'FakeMessage';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    action: Attribute.Component<'base.action'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    fakeMessage: Attribute.Component<'base.message', true> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface SectionsDistributorsList extends Schema.Component {
  collectionName: 'components_sections_distributors_lists';
  info: {
    displayName: 'DistributorsList';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
      customi18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'Distributors'>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    action: Attribute.Component<'base.action'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface SectionsCustom extends Schema.Component {
  collectionName: 'components_sections_customs';
  info: {
    displayName: 'Custom';
    icon: 'cog';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
      customi18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    customComponents: Attribute.Relation<
      'sections.custom',
      'oneToMany',
      'api::custom-component.custom-component'
    > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface SectionsCta extends Schema.Component {
  collectionName: 'components_sections_cta';
  info: {
    displayName: 'CTA';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'CTA'>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    media: Attribute.Media<'images'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    actions: Attribute.Component<'base.action', true> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    variant: Attribute.Relation<
      'sections.cta',
      'oneToOne',
      'api::variants.variant'
    > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
        customi18n: {
          localized: false;
        };
      }>;
  };
}

export interface SectionsCtaList extends Schema.Component {
  collectionName: 'components_sections_cta_lists';
  info: {
    displayName: 'CTAList';
    icon: 'bulletList';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'CTA List'>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    ctas: Attribute.Component<'sections.cta', true> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    variant: Attribute.Relation<
      'sections.cta-list',
      'oneToOne',
      'api::variants.variant'
    > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
        customi18n: {
          localized: false;
        };
      }>;
  };
}

export interface SectionsCountrySelector extends Schema.Component {
  collectionName: 'components_sections_country_selectors';
  info: {
    displayName: 'ServicesCountrySelector';
    icon: 'layer';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }> &
      Attribute.DefaultTo<'Country selector'>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    services: Attribute.JSON;
    input: Attribute.Component<'base.input'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    countryList: Attribute.JSON;
  };
}

export interface SectionsCountryFlag extends Schema.Component {
  collectionName: 'components_sections_country_flags';
  info: {
    displayName: 'CountryFlag';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'Country flags'>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    countries: Attribute.Relation<
      'sections.country-flag',
      'oneToMany',
      'api::core-country.core-country'
    > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    service: Attribute.Enumeration<
      ['Remittance', 'Top-Up', 'GiftCard', 'None']
    > &
      Attribute.DefaultTo<'None'>;
    action: Attribute.Component<'base.action'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface SectionsContentCta extends Schema.Component {
  collectionName: 'components_sections_content_ctas';
  info: {
    displayName: 'ContentCTA';
    icon: 'layer';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    content: Attribute.Component<'base.rich-text', true> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    action: Attribute.Component<'base.action'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface SectionsContactUsForm extends Schema.Component {
  collectionName: 'components_sections_contact_us_forms';
  info: {
    displayName: 'ContactUsForm';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    emailButtonText: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    chatButtonText: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbarBaloon';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    formDescription: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    FAQxCtgSelectorTitle: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    FAQSelectorTitle: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    noAnswerText: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    Inputs: Attribute.Component<'base.input', true> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    checkBoxText: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    action: Attribute.Component<'base.action'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface SectionsBlogsByCategory extends Schema.Component {
  collectionName: 'components_sections_blogs_by_categories';
  info: {
    displayName: 'BlogsByCategory';
    icon: 'dashboard';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
      customi18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'PostByCategories'>;
    moreDetailButtonText: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
        customi18n: {
          localized: true;
        };
      }>;
    allCategoriesLabel: Attribute.String;
    searchInput: Attribute.Component<'base.input'>;
  };
}

export interface SectionsBanner extends Schema.Component {
  collectionName: 'components_sections_banners';
  info: {
    displayName: 'Banner';
    icon: 'paintBrush';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'Banner'>;
    image: Attribute.Media<'images'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    actions: Attribute.Component<'base.action', true> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface SectionsArticle extends Schema.Component {
  collectionName: 'components_sections_articles';
  info: {
    displayName: 'Article';
    icon: 'quote';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'Article'>;
    article: Attribute.Relation<
      'sections.article',
      'oneToOne',
      'api::news-post.news-post'
    >;
  };
}

export interface SectionsApplyJobForm extends Schema.Component {
  collectionName: 'components_sections_apply_job_forms';
  info: {
    displayName: 'ApplyJobForm';
    icon: 'envelop';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'ApplyJobForm'>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    inputs: Attribute.Component<'base.input', true> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    action: Attribute.Component<'base.action'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    emailTo: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
  };
}

export interface SectionsAllCountrySearch extends Schema.Component {
  collectionName: 'components_sections_all_country_searches';
  info: {
    displayName: 'All countrySearch';
    icon: 'search';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'AllCountriesSearch'>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    countryList: Attribute.JSON;
    services: Attribute.JSON;
    input: Attribute.Component<'base.input'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    sortingButtonAction: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface BaseUrl extends Schema.Component {
  collectionName: 'components_base_urls';
  info: {
    displayName: 'Url';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'URL'>;
    url: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: false;
        };
      }>;
  };
}

export interface BaseTitleledFeatureList extends Schema.Component {
  collectionName: 'components_base_titleled_feature_lists';
  info: {
    displayName: 'titleledFeatureList';
    icon: 'command';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    title: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    features: Attribute.Component<'base.feature', true> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    action: Attribute.Component<'base.action'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface BaseRichText extends Schema.Component {
  collectionName: 'components_base_rich_texts';
  info: {
    displayName: 'RichText';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'Rich text'>;
    text: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface BaseMessage extends Schema.Component {
  collectionName: 'components_base_messages';
  info: {
    displayName: 'Message';
    description: 'A component used to represent a fake client message';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    userPict: Attribute.Media<'images'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    userName: Attribute.String &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    message: Attribute.Text &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    date: Attribute.Date &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
  };
}

export interface BaseMedia extends Schema.Component {
  collectionName: 'components_base_media';
  info: {
    displayName: 'Media';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    media: Attribute.Media<'images'> &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface BaseLogo extends Schema.Component {
  collectionName: 'components_base_logos';
  info: {
    displayName: 'logo';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }> &
      Attribute.DefaultTo<'Logo'>;
    logo: Attribute.Media<'images'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface BaseInput extends Schema.Component {
  collectionName: 'components_base_inputs';
  info: {
    displayName: 'input';
    icon: 'pencil';
    description: '';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    isRequired: Attribute.Boolean &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }> &
      Attribute.DefaultTo<false>;
    isSearchInput: Attribute.Boolean &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }> &
      Attribute.DefaultTo<false>;
    title: Attribute.Text &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    hint: Attribute.Text &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    placeholder: Attribute.Text &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    errorText: Attribute.Text &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface BaseFeature extends Schema.Component {
  collectionName: 'components_base_features';
  info: {
    displayName: 'Feature';
    description: 'Used to show an icon image and a heading component';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'FeatureName'>;
    icon: Attribute.Media<'images'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: false;
        };
      }>;
    badgeText: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    title: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface BaseFakeReview extends Schema.Component {
  collectionName: 'components_base_fake_reviews';
  info: {
    displayName: 'FakeReview';
    description: 'Component used to represent a fake client review';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    userName: Attribute.String &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    stars: Attribute.Integer &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    verified: Attribute.Boolean &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    reviewText: Attribute.Text &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    location_time: Attribute.Text &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
  };
}

export interface BaseCustomUrl extends Schema.Component {
  collectionName: 'components_base_custom_urls';
  info: {
    displayName: 'customUrl';
    icon: 'earth';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    url: Attribute.JSON &
      Attribute.CustomField<'plugin::strapi-plugin-ui-fields.page-selector-filter'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
          model: 'api::page.page';
          field: 'page';
        };
      }>;
  };
}

export interface BaseAction extends Schema.Component {
  collectionName: 'components_base_actions';
  info: {
    displayName: 'Action';
    description: 'A component used to do something, represented as a link, a button, an icon-button, etc';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'Action'>;
    url: Attribute.Component<'base.custom-url'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: false;
        };
      }>;
    image: Attribute.Media<'images'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
    openIn: Attribute.Enumeration<['sameTab', 'newTab', 'newWindow', 'modal']> &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }> &
      Attribute.DefaultTo<'sameTab'>;
    text: Attribute.Text &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

export interface BaseActionList extends Schema.Component {
  collectionName: 'components_base_action_lists';
  info: {
    displayName: 'ActionsList';
    description: '';
    placeholder: 'List of actions';
  };
  options: {
    pluginOptions: {
      i18n: {
        localized: true;
      };
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'Action list'>;
    actions: Attribute.Component<'base.action', true> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
        customi18n: {
          localized: true;
        };
      }>;
  };
}

declare module '@strapi/types' {
  export module Shared {
    export interface Components {
      'shared.url-group': SharedUrlGroup;
      'shared.service-button': SharedServiceButton;
      'shared.seo': SharedSeo;
      'shared.popover-page-action': SharedPopoverPageAction;
      'shared.navbar-country-selector': SharedNavbarCountrySelector;
      'shared.meta-social': SharedMetaSocial;
      'shared.manage-layout-modal': SharedManageLayoutModal;
      'shared.location': SharedLocation;
      'shared.event-scheduler': SharedEventScheduler;
      'shared.discount-code': SharedDiscountCode;
      'shared.delete-service-type': SharedDeleteServiceType;
      'shared.custom-dynamic-zone': SharedCustomDynamicZone;
      'shared.contact-information': SharedContactInformation;
      'shared.caption-by-page': SharedCaptionByPage;
      'meta.social-media': MetaSocialMedia;
      'meta.metadata': MetaMetadata;
      'sections.text-list': SectionsTextList;
      'sections.shop-list': SectionsShopList;
      'sections.send-email-form': SectionsSendEmailForm;
      'sections.review': SectionsReview;
      'sections.promotions-list': SectionsPromotionsList;
      'sections.promotion': SectionsPromotion;
      'sections.partners-list': SectionsPartnersList;
      'sections.operators-list': SectionsOperatorsList;
      'sections.notification-banner': SectionsNotificationBanner;
      'sections.news-list': SectionsNewsList;
      'sections.logo-list': SectionsLogoList;
      'sections.job-offers-list': SectionsJobOffersList;
      'sections.job-offer': SectionsJobOffer;
      'sections.hero': SectionsHero;
      'sections.heading': SectionsHeading;
      'sections.heading-list': SectionsHeadingList;
      'sections.feature-list': SectionsFeatureList;
      'sections.feat-list-acordion': SectionsFeatListAcordion;
      'sections.faq-list': SectionsFaqList;
      'sections.faq-by-categories': SectionsFaqByCategories;
      'sections.fake-msg': SectionsFakeMsg;
      'sections.distributors-list': SectionsDistributorsList;
      'sections.custom': SectionsCustom;
      'sections.cta': SectionsCta;
      'sections.cta-list': SectionsCtaList;
      'sections.country-selector': SectionsCountrySelector;
      'sections.country-flag': SectionsCountryFlag;
      'sections.content-cta': SectionsContentCta;
      'sections.contact-us-form': SectionsContactUsForm;
      'sections.blogs-by-category': SectionsBlogsByCategory;
      'sections.banner': SectionsBanner;
      'sections.article': SectionsArticle;
      'sections.apply-job-form': SectionsApplyJobForm;
      'sections.all-country-search': SectionsAllCountrySearch;
      'base.url': BaseUrl;
      'base.titleled-feature-list': BaseTitleledFeatureList;
      'base.rich-text': BaseRichText;
      'base.message': BaseMessage;
      'base.media': BaseMedia;
      'base.logo': BaseLogo;
      'base.input': BaseInput;
      'base.feature': BaseFeature;
      'base.fake-review': BaseFakeReview;
      'base.custom-url': BaseCustomUrl;
      'base.action': BaseAction;
      'base.action-list': BaseActionList;
    }
  }
}
