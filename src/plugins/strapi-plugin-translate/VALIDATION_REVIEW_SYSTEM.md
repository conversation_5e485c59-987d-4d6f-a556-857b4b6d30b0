# Validation Review System

## Overview

The Validation Review System is a comprehensive feature that captures, stores, and displays field validation changes made during translation processes. This system helps users understand what modifications were automatically applied to their content to meet field validation requirements.

## Features

- **Automatic Change Detection**: Captures validation changes during translation
- **Persistent Storage**: Stores changes both server-side and client-side
- **Review Interface**: Provides a user-friendly modal to review changes
- **Change Categorization**: Groups changes by type, field, and severity
- **Clear on Retranslate**: Automatically clears old changes when starting new translations

## Architecture

### Backend Components

#### 1. Validation Changes Service (`server/services/validation-changes.js`)

The core service that manages validation changes data:

```javascript
// Store validation changes
await strapi.service('plugin::translate.validation-changes').storeValidationChanges(
  contentType,
  locale,
  entityId,
  changes
)

// Retrieve validation changes
const changes = await strapi.service('plugin::translate.validation-changes').getValidationChanges(
  contentType,
  locale
)
```

**Key Methods:**
- `storeValidationChanges(contentType, locale, entityId, changes)` - Store changes
- `getValidationChanges(contentType, locale)` - Retrieve changes
- `clearValidationChanges(contentType, locale)` - Clear changes
- `hasValidationChanges(contentType, locale)` - Check if changes exist
- `formatChangesForFrontend(changes, contentType, locale)` - Format for UI

#### 2. Enhanced Translation Controller (`server/controllers/translate.js`)

Modified to capture and return validation changes:

```javascript
// Apply field validation and capture changes
const validationResult = autoCorrectTranslatedContent(cleanedData, contentTypeUid, {
  enableAutoCorrection: true
})

// Store validation changes if any were made
if (validationResult.corrected && validationResult.changes.length > 0) {
  await getService('validation-changes').storeValidationChanges(
    contentTypeUid,
    targetLocale,
    id,
    validationResult.changes
  )
}
```

#### 3. New API Routes (`server/routes/translate.js`)

Added routes for validation changes management:

- `GET /validation-changes` - Get changes for content type and locale
- `GET /validation-changes/content-type` - Get all changes for content type
- `POST /validation-changes/clear` - Clear changes
- `GET /validation-changes/summary` - Get summary of all changes

### Frontend Components

#### 1. ValidationChangesModal (`admin/src/components/ValidationChangesModal/index.js`)

A comprehensive modal component that displays validation changes:

**Features:**
- Summary view with change counts
- Detailed table of all changes
- Severity indicators (High/Medium/Low)
- Clear changes functionality
- Empty state handling

#### 2. Enhanced CollectionRow (`admin/src/components/Collection/CollectionRow.js`)

Added "Review Changes" button to the action buttons:

```jsx
<IconButton
  data-cy={`${entry.contentType}.${locale.code}.review-changes`}
  onClick={() => onAction('review-changes', locale.code)}
  label="Review Changes"
  icon={<Eye />}
/>
```

#### 3. API Utilities (`admin/src/utils/validationChangesApi.js`)

Comprehensive API utilities for frontend-backend communication:

**Server Communication:**
- `fetchValidationChanges(contentType, locale)`
- `clearValidationChanges(contentType, locale)`
- `fetchValidationChangesSummary()`

**Local Storage Management:**
- `saveValidationChangesToStorage(contentType, locale, changes)`
- `loadValidationChangesFromStorage(contentType, locale)`
- `clearValidationChangesFromStorage(contentType, locale)`

## Usage Guide

### For Developers

#### 1. Accessing Validation Changes Programmatically

```javascript
// In a Strapi controller or service
const validationChangesService = strapi.service('plugin::translate.validation-changes')

// Check if changes exist
const hasChanges = validationChangesService.hasValidationChanges('api::page.page', 'es')

// Get changes
const changes = validationChangesService.getValidationChanges('api::page.page', 'es')

// Format for frontend
const formatted = validationChangesService.formatChangesForFrontend(
  changes.changes,
  'api::page.page',
  'es'
)
```

#### 2. Frontend Integration

```javascript
// In a React component
import { fetchValidationChanges } from '../../utils/validationChangesApi'

const loadChanges = async () => {
  try {
    const response = await fetchValidationChanges(contentType, locale)
    setValidationChanges(response.data)
  } catch (error) {
    console.error('Failed to load validation changes:', error)
  }
}
```

### For Content Managers

#### 1. Reviewing Changes

1. Navigate to the translation management interface
2. Look for the "Review Changes" button (eye icon) next to each locale
3. Click the button to open the validation changes modal
4. Review the summary and detailed changes
5. Optionally clear changes when no longer needed

#### 2. Understanding Change Types

- **Truncation (High Severity)**: Content was shortened to meet maximum length requirements
- **Padding (Medium Severity)**: Content was extended to meet minimum length requirements
- **Correction (Low Severity)**: Minor corrections were applied

## Data Structure

### Change Object Structure

```javascript
{
  type: 'truncation' | 'padding' | 'correction',
  field: 'field.path.name',
  originalLength: 100,
  newLength: 60,
  method: 'word' | 'sentence' | 'character',
  message: 'Human-readable description',
  timestamp: '2024-01-01T00:00:00.000Z'
}
```

### Storage Structure

```javascript
{
  contentType: 'api::page.page',
  locale: 'es',
  entityId: 123,
  changes: [/* array of change objects */],
  timestamp: '2024-01-01T00:00:00.000Z',
  totalChanges: 3,
  hasChanges: true,
  changesByType: {
    truncation: [/* changes */],
    padding: [/* changes */]
  },
  changesByField: {
    'title': [/* changes */],
    'seo.metaTitle': [/* changes */]
  }
}
```

## Configuration

### Field Validation Rules

The system automatically detects validation rules from your content-type schemas:

```javascript
// In your content-type schema
{
  attributes: {
    title: {
      type: 'string',
      maxLength: 60  // Will trigger truncation if exceeded
    },
    description: {
      type: 'text',
      minLength: 50  // Will trigger padding if below minimum
    }
  }
}
```

### Auto-Correction Options

```javascript
const options = {
  enableAutoCorrection: true,  // Enable/disable auto-correction
  addEllipsis: true,          // Add ellipsis when truncating
  preserveHTML: true,         // Preserve HTML tags
  preserveMarkdown: true,     // Preserve Markdown formatting
  strategy: 'auto'            // Padding strategy
}
```

## Troubleshooting

### Common Issues

1. **Changes Not Appearing**
   - Ensure the translation process completed successfully
   - Check that field validation rules are properly defined in your schema
   - Verify that auto-correction is enabled

2. **Modal Not Opening**
   - Check browser console for JavaScript errors
   - Ensure the ValidationChangesModal component is properly imported
   - Verify API routes are accessible

3. **Changes Not Persisting**
   - Check server logs for storage errors
   - Verify localStorage is available and not full
   - Ensure proper permissions for the validation-changes service

### Debug Mode

Enable debug logging by setting the log level:

```javascript
// In your Strapi configuration
module.exports = {
  logger: {
    level: 'debug'
  }
}
```

## Performance Considerations

- Changes are stored in memory on the server side for fast access
- Frontend uses localStorage for offline access and performance
- Large numbers of changes are paginated in the UI
- Automatic cleanup prevents memory leaks

## Security

- All validation changes routes require admin authentication
- Content-manager permissions are enforced
- No sensitive data is stored in validation changes
- Client-side storage uses safe JSON serialization

## Future Enhancements

- Export validation changes to CSV/JSON
- Email notifications for significant changes
- Integration with audit logs
- Batch operations for multiple content types
- Custom validation rules and corrections
