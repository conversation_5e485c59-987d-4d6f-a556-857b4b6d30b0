import React, { useState, useEffect } from 'react'
import { useIntl } from 'react-intl'
import { useNotification } from '@strapi/helper-plugin'
import {
  Dialog,
  DialogBody,
  DialogFooter,
} from '@strapi/design-system/Dialog'
import { Button } from '@strapi/design-system/Button'
import { Typography } from '@strapi/design-system/Typography'
import { Stack } from '@strapi/design-system/Stack'
import { Flex } from '@strapi/design-system/Flex'
import { Box } from '@strapi/design-system/Box'
import { Badge } from '@strapi/design-system/Badge'
import { Table, Thead, Tbody, Tr, Td, Th } from '@strapi/design-system/Table'
import { Divider } from '@strapi/design-system/Divider'
import { EmptyStateLayout } from '@strapi/design-system/EmptyStateLayout'
import { Loader } from '@strapi/design-system/Loader'
import ExclamationMarkCircle from '@strapi/icons/ExclamationMarkCircle'
import EmptyDocuments from '@strapi/icons/EmptyDocuments'
import PropTypes from 'prop-types'
import { getTrad } from '../../utils'
import { fetchValidationChanges, clearValidationChanges } from '../../utils/validationChangesApi'

const ValidationChangesModal = ({ 
  isOpen, 
  onClose, 
  contentType, 
  locale,
  onChangesCleared 
}) => {
  const { formatMessage } = useIntl()
  const toggleNotification = useNotification()
  const [validationChanges, setValidationChanges] = useState(null)
  const [loading, setLoading] = useState(false)
  const [clearing, setClearing] = useState(false)

  useEffect(() => {
    if (isOpen && contentType && locale) {
      loadValidationChanges()
    }
  }, [isOpen, contentType, locale])

  const loadValidationChanges = async () => {
    setLoading(true)
    try {
      const response = await fetchValidationChanges(contentType, locale)
      setValidationChanges(response.data)
    } catch (error) {
      console.error('Failed to load validation changes:', error)
      toggleNotification({
        type: 'warning',
        message: {
          id: getTrad('validation-changes.error.failed-to-load'),
          defaultMessage: 'Failed to load validation changes',
        },
      })
      setValidationChanges(null)
    } finally {
      setLoading(false)
    }
  }

  const handleClearChanges = async () => {
    if (!window.confirm(formatMessage({
      id: getTrad('validation-changes.modal.clear-changes.confirm'),
      defaultMessage: 'Are you sure you want to clear all validation changes for this content type and locale?',
    }))) {
      return
    }

    setClearing(true)
    try {
      await clearValidationChanges(contentType, locale)
      toggleNotification({
        type: 'success',
        message: {
          id: getTrad('validation-changes.success.cleared'),
          defaultMessage: 'Validation changes cleared successfully',
        },
      })
      setValidationChanges(null)
      if (onChangesCleared) {
        onChangesCleared(contentType, locale)
      }
    } catch (error) {
      console.error('Failed to clear validation changes:', error)
      toggleNotification({
        type: 'warning',
        message: {
          id: getTrad('validation-changes.error.failed-to-clear'),
          defaultMessage: 'Failed to clear validation changes',
        },
      })
    } finally {
      setClearing(false)
    }
  }

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'high':
        return 'danger500'
      case 'medium':
        return 'warning500'
      case 'low':
        return 'success500'
      default:
        return 'neutral500'
    }
  }

  const getTypeDisplayName = (type) => {
    return formatMessage({
      id: getTrad(`validation-changes.modal.type.${type}`),
      defaultMessage: type,
    })
  }

  const renderSummary = () => {
    if (!validationChanges || !validationChanges.hasChanges) {
      return null
    }

    const { summary } = validationChanges

    return (
      <Box>
        <Typography variant="beta" marginBottom={3}>
          {formatMessage({
            id: getTrad('validation-changes.modal.summary.title'),
            defaultMessage: 'Summary',
          })}
        </Typography>
        
        <Stack spacing={2}>
          <Flex justifyContent="space-between">
            <Typography>
              {formatMessage({
                id: getTrad('validation-changes.modal.summary.total-changes'),
                defaultMessage: 'Total Changes',
              })}:
            </Typography>
            <Badge backgroundColor="primary500" textColor="neutral100">
              {summary.totalChanges}
            </Badge>
          </Flex>

          {summary.byType && Object.keys(summary.byType).length > 0 && (
            <Box>
              <Typography variant="delta" marginBottom={2}>
                {formatMessage({
                  id: getTrad('validation-changes.modal.summary.by-type'),
                  defaultMessage: 'Changes by Type',
                })}:
              </Typography>
              <Flex wrap="wrap" gap={2}>
                {Object.entries(summary.byType).map(([type, count]) => (
                  <Badge key={type} backgroundColor="neutral200" textColor="neutral800">
                    {getTypeDisplayName(type)}: {count}
                  </Badge>
                ))}
              </Flex>
            </Box>
          )}
        </Stack>
      </Box>
    )
  }

  const renderChangesTable = () => {
    if (!validationChanges || !validationChanges.hasChanges) {
      return null
    }

    const { changes } = validationChanges

    return (
      <Box>
        <Typography variant="beta" marginBottom={3}>
          {formatMessage({
            id: getTrad('validation-changes.modal.changes.title'),
            defaultMessage: 'Detailed Changes',
          })}
        </Typography>
        
        <Table colCount={5} rowCount={changes.length + 1}>
          <Thead>
            <Tr>
              <Th>
                <Typography variant="sigma">
                  {formatMessage({
                    id: getTrad('validation-changes.modal.changes.field'),
                    defaultMessage: 'Field',
                  })}
                </Typography>
              </Th>
              <Th>
                <Typography variant="sigma">
                  {formatMessage({
                    id: getTrad('validation-changes.modal.changes.type'),
                    defaultMessage: 'Type',
                  })}
                </Typography>
              </Th>
              <Th>
                <Typography variant="sigma">
                  {formatMessage({
                    id: getTrad('validation-changes.modal.changes.original-length'),
                    defaultMessage: 'Original Length',
                  })}
                </Typography>
              </Th>
              <Th>
                <Typography variant="sigma">
                  {formatMessage({
                    id: getTrad('validation-changes.modal.changes.new-length'),
                    defaultMessage: 'New Length',
                  })}
                </Typography>
              </Th>
              <Th>
                <Typography variant="sigma">
                  {formatMessage({
                    id: getTrad('validation-changes.modal.changes.description'),
                    defaultMessage: 'Description',
                  })}
                </Typography>
              </Th>
            </Tr>
          </Thead>
          <Tbody>
            {changes.map((change, index) => (
              <Tr key={index}>
                <Td>
                  <Typography textColor="neutral800">
                    {change.field}
                  </Typography>
                </Td>
                <Td>
                  <Badge 
                    backgroundColor={getSeverityColor(change.severity || 'medium')}
                    textColor="neutral100"
                  >
                    {getTypeDisplayName(change.type)}
                  </Badge>
                </Td>
                <Td>
                  <Typography textColor="neutral800">
                    {change.originalLength || '-'}
                  </Typography>
                </Td>
                <Td>
                  <Typography textColor="neutral800">
                    {change.newLength || '-'}
                  </Typography>
                </Td>
                <Td>
                  <Typography textColor="neutral800">
                    {change.message}
                  </Typography>
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </Box>
    )
  }

  const renderContent = () => {
    if (loading) {
      return (
        <Flex justifyContent="center" padding={6}>
          <Loader>Loading validation changes...</Loader>
        </Flex>
      )
    }

    if (!validationChanges || !validationChanges.hasChanges) {
      return (
        <EmptyStateLayout
          icon={<EmptyDocuments width="10rem" height="10rem" />}
          content={formatMessage({
            id: getTrad('validation-changes.modal.no-changes'),
            defaultMessage: 'No validation changes found for this content type and locale.',
          })}
        />
      )
    }

    return (
      <Stack spacing={4}>
        {renderSummary()}
        <Divider />
        {renderChangesTable()}
      </Stack>
    )
  }

  return (
    <Dialog
      onClose={onClose}
      title={formatMessage({
        id: getTrad('validation-changes.modal.title'),
        defaultMessage: 'Validation Changes Review',
      })}
      isOpen={isOpen}
      size="L"
    >
      <DialogBody icon={<ExclamationMarkCircle />}>
        <Box padding={2}>
          {renderContent()}
        </Box>
      </DialogBody>
      <DialogFooter
        startAction={
          validationChanges && validationChanges.hasChanges && (
            <Button 
              onClick={handleClearChanges} 
              variant="danger-light"
              loading={clearing}
            >
              {formatMessage({
                id: getTrad('validation-changes.modal.clear-changes'),
                defaultMessage: 'Clear Changes',
              })}
            </Button>
          )
        }
        endAction={
          <Button onClick={onClose} variant="tertiary">
            {formatMessage({
              id: getTrad('validation-changes.modal.close'),
              defaultMessage: 'Close',
            })}
          </Button>
        }
      />
    </Dialog>
  )
}

ValidationChangesModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  contentType: PropTypes.string,
  locale: PropTypes.string,
  onChangesCleared: PropTypes.func,
}

export default ValidationChangesModal
