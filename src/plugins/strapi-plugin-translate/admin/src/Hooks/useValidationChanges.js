import { useState, useEffect, useCallback } from 'react'
import { 
  fetchAllValidationChangesForContentType,
  loadValidationChangesFromStorage,
  syncValidationChanges 
} from '../utils/validationChangesApi'

/**
 * Custom hook to manage validation changes state across content types and locales
 * Provides functionality to check if validation changes exist for specific combinations
 */
const useValidationChanges = (collections, locales) => {
  const [validationChangesMap, setValidationChangesMap] = useState(new Map())
  const [loading, setLoading] = useState(false)

  /**
   * Check if validation changes exist for a specific content type and locale
   * @param {string} contentType - The content-type UID
   * @param {string} locale - The locale code
   * @returns {boolean} - True if changes exist
   */
  const hasValidationChanges = useCallback((contentType, locale) => {
    const key = `${contentType}:${locale}`
    const changes = validationChangesMap.get(key)
    const hasChanges = changes && changes.hasChanges && changes.changes && changes.changes.length > 0

    console.log(`🔍 Checking validation changes for ${key}:`, {
      found: !!changes,
      hasChanges: changes?.hasChanges,
      changesLength: changes?.changes?.length,
      result: hasChanges
    })

    return hasChanges
  }, [validationChangesMap])

  /**
   * Load validation changes for a specific content type from both server and localStorage
   * @param {string} contentType - The content-type UID
   */
  const loadValidationChangesForContentType = useCallback(async (contentType) => {
    console.log(`🔍 Loading validation changes for content type: ${contentType}`)

    try {
      // Try to fetch from server first
      const serverResponse = await fetchAllValidationChangesForContentType(contentType)
      console.log(`📡 Server response for ${contentType}:`, serverResponse)

      if (serverResponse.data && Object.keys(serverResponse.data).length > 0) {
        console.log(`✅ Found server data for ${contentType}:`, Object.keys(serverResponse.data))
        // Update map with server data
        Object.entries(serverResponse.data).forEach(([locale, changes]) => {
          const key = `${contentType}:${locale}`
          console.log(`💾 Storing validation changes for ${key}:`, changes.hasChanges, changes.changes?.length)
          setValidationChangesMap(prev => new Map(prev.set(key, changes)))
        })
      } else {
        console.log(`📭 No server data for ${contentType}, checking localStorage`)
        // Fallback to localStorage for each locale
        locales.forEach(locale => {
          const storedChanges = loadValidationChangesFromStorage(contentType, locale.code)
          if (storedChanges && storedChanges.hasChanges) {
            const key = `${contentType}:${locale.code}`
            console.log(`💾 Found localStorage data for ${key}:`, storedChanges.changes?.length)
            setValidationChangesMap(prev => new Map(prev.set(key, storedChanges)))
          }
        })
      }
    } catch (error) {
      console.warn(`❌ Failed to load validation changes for ${contentType}:`, error)

      // Fallback to localStorage
      locales.forEach(locale => {
        const storedChanges = loadValidationChangesFromStorage(contentType, locale.code)
        if (storedChanges && storedChanges.hasChanges) {
          const key = `${contentType}:${locale.code}`
          console.log(`💾 Fallback localStorage data for ${key}:`, storedChanges.changes?.length)
          setValidationChangesMap(prev => new Map(prev.set(key, storedChanges)))
        }
      })
    }
  }, [locales])

  /**
   * Load validation changes for all content types
   */
  const loadAllValidationChanges = useCallback(async () => {
    if (!collections || collections.length === 0 || !locales || locales.length === 0) {
      return
    }

    setLoading(true)
    
    try {
      // Load validation changes for each content type
      await Promise.all(
        collections.map(collection => 
          loadValidationChangesForContentType(collection.contentType)
        )
      )
    } catch (error) {
      console.error('Failed to load validation changes:', error)
    } finally {
      setLoading(false)
    }
  }, [collections, locales, loadValidationChangesForContentType])

  /**
   * Refresh validation changes for a specific content type and locale
   * @param {string} contentType - The content-type UID
   * @param {string} locale - The locale code
   */
  const refreshValidationChanges = useCallback(async (contentType, locale) => {
    console.log(`🔄 Refreshing validation changes for ${contentType}:${locale}`)

    try {
      const changes = await syncValidationChanges(contentType, locale)
      const key = `${contentType}:${locale}`

      console.log(`📡 Sync result for ${key}:`, changes)

      if (changes && changes.hasChanges) {
        console.log(`✅ Setting validation changes for ${key}:`, changes.changes?.length)
        setValidationChangesMap(prev => new Map(prev.set(key, changes)))
      } else {
        console.log(`🗑️ Removing validation changes for ${key} (no changes found)`)
        // Remove from map if no changes exist
        setValidationChangesMap(prev => {
          const newMap = new Map(prev)
          newMap.delete(key)
          return newMap
        })
      }
    } catch (error) {
      console.warn(`❌ Failed to refresh validation changes for ${contentType}:${locale}:`, error)
    }
  }, [])

  /**
   * Clear validation changes from the map (when changes are cleared)
   * @param {string} contentType - The content-type UID
   * @param {string} locale - The locale code
   */
  const clearValidationChangesFromMap = useCallback((contentType, locale) => {
    const key = `${contentType}:${locale}`
    setValidationChangesMap(prev => {
      const newMap = new Map(prev)
      newMap.delete(key)
      return newMap
    })
  }, [])

  /**
   * Get validation changes count for a specific content type and locale
   * @param {string} contentType - The content-type UID
   * @param {string} locale - The locale code
   * @returns {number} - Number of validation changes
   */
  const getValidationChangesCount = useCallback((contentType, locale) => {
    const key = `${contentType}:${locale}`
    const changes = validationChangesMap.get(key)
    return changes && changes.changes ? changes.changes.length : 0
  }, [validationChangesMap])

  /**
   * Get validation changes summary for a specific content type and locale
   * @param {string} contentType - The content-type UID
   * @param {string} locale - The locale code
   * @returns {object|null} - Validation changes summary or null
   */
  const getValidationChangesSummary = useCallback((contentType, locale) => {
    const key = `${contentType}:${locale}`
    return validationChangesMap.get(key) || null
  }, [validationChangesMap])

  // Load validation changes when collections or locales change
  useEffect(() => {
    loadAllValidationChanges()
  }, [loadAllValidationChanges])

  return {
    hasValidationChanges,
    getValidationChangesCount,
    getValidationChangesSummary,
    refreshValidationChanges,
    clearValidationChangesFromMap,
    loadValidationChangesForContentType,
    loading,
    validationChangesMap
  }
}

export default useValidationChanges
