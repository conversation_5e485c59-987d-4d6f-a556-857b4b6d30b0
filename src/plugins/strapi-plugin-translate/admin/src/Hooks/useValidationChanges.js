import { useState, useEffect, useCallback } from 'react'
import { 
  fetchAllValidationChangesForContentType,
  loadValidationChangesFromStorage,
  syncValidationChanges 
} from '../utils/validationChangesApi'

/**
 * Custom hook to manage validation changes state across content types and locales
 * Provides functionality to check if validation changes exist for specific combinations
 */
const useValidationChanges = (collections, locales) => {
  const [validationChangesMap, setValidationChangesMap] = useState(new Map())
  const [loading, setLoading] = useState(false)

  /**
   * Check if validation changes exist for a specific content type and locale
   * @param {string} contentType - The content-type UID
   * @param {string} locale - The locale code
   * @returns {boolean} - True if changes exist
   */
  const hasValidationChanges = useCallback((contentType, locale) => {
    const key = `${contentType}:${locale}`
    const changes = validationChangesMap.get(key)
    return changes && changes.hasChanges && changes.changes && changes.changes.length > 0
  }, [validationChangesMap])

  /**
   * Load validation changes for a specific content type from both server and localStorage
   * @param {string} contentType - The content-type UID
   */
  const loadValidationChangesForContentType = useCallback(async (contentType) => {
    try {
      // Try to fetch from server first
      const serverResponse = await fetchAllValidationChangesForContentType(contentType)
      
      if (serverResponse.data && Object.keys(serverResponse.data).length > 0) {
        // Update map with server data
        Object.entries(serverResponse.data).forEach(([locale, changes]) => {
          const key = `${contentType}:${locale}`
          setValidationChangesMap(prev => new Map(prev.set(key, changes)))
        })
      } else {
        // Fallback to localStorage for each locale
        locales.forEach(locale => {
          const storedChanges = loadValidationChangesFromStorage(contentType, locale.code)
          if (storedChanges && storedChanges.hasChanges) {
            const key = `${contentType}:${locale.code}`
            setValidationChangesMap(prev => new Map(prev.set(key, storedChanges)))
          }
        })
      }
    } catch (error) {
      console.warn(`Failed to load validation changes for ${contentType}:`, error)
      
      // Fallback to localStorage
      locales.forEach(locale => {
        const storedChanges = loadValidationChangesFromStorage(contentType, locale.code)
        if (storedChanges && storedChanges.hasChanges) {
          const key = `${contentType}:${locale.code}`
          setValidationChangesMap(prev => new Map(prev.set(key, storedChanges)))
        }
      })
    }
  }, [locales])

  /**
   * Load validation changes for all content types
   */
  const loadAllValidationChanges = useCallback(async () => {
    if (!collections || collections.length === 0 || !locales || locales.length === 0) {
      return
    }

    setLoading(true)
    
    try {
      // Load validation changes for each content type
      await Promise.all(
        collections.map(collection => 
          loadValidationChangesForContentType(collection.contentType)
        )
      )
    } catch (error) {
      console.error('Failed to load validation changes:', error)
    } finally {
      setLoading(false)
    }
  }, [collections, locales, loadValidationChangesForContentType])

  /**
   * Refresh validation changes for a specific content type and locale
   * @param {string} contentType - The content-type UID
   * @param {string} locale - The locale code
   */
  const refreshValidationChanges = useCallback(async (contentType, locale) => {
    try {
      const changes = await syncValidationChanges(contentType, locale)
      const key = `${contentType}:${locale}`
      
      if (changes && changes.hasChanges) {
        setValidationChangesMap(prev => new Map(prev.set(key, changes)))
      } else {
        // Remove from map if no changes exist
        setValidationChangesMap(prev => {
          const newMap = new Map(prev)
          newMap.delete(key)
          return newMap
        })
      }
    } catch (error) {
      console.warn(`Failed to refresh validation changes for ${contentType}:${locale}:`, error)
    }
  }, [])

  /**
   * Clear validation changes from the map (when changes are cleared)
   * @param {string} contentType - The content-type UID
   * @param {string} locale - The locale code
   */
  const clearValidationChangesFromMap = useCallback((contentType, locale) => {
    const key = `${contentType}:${locale}`
    setValidationChangesMap(prev => {
      const newMap = new Map(prev)
      newMap.delete(key)
      return newMap
    })
  }, [])

  /**
   * Get validation changes count for a specific content type and locale
   * @param {string} contentType - The content-type UID
   * @param {string} locale - The locale code
   * @returns {number} - Number of validation changes
   */
  const getValidationChangesCount = useCallback((contentType, locale) => {
    const key = `${contentType}:${locale}`
    const changes = validationChangesMap.get(key)
    return changes && changes.changes ? changes.changes.length : 0
  }, [validationChangesMap])

  /**
   * Get validation changes summary for a specific content type and locale
   * @param {string} contentType - The content-type UID
   * @param {string} locale - The locale code
   * @returns {object|null} - Validation changes summary or null
   */
  const getValidationChangesSummary = useCallback((contentType, locale) => {
    const key = `${contentType}:${locale}`
    return validationChangesMap.get(key) || null
  }, [validationChangesMap])

  // Load validation changes when collections or locales change
  useEffect(() => {
    loadAllValidationChanges()
  }, [loadAllValidationChanges])

  return {
    hasValidationChanges,
    getValidationChangesCount,
    getValidationChangesSummary,
    refreshValidationChanges,
    clearValidationChangesFromMap,
    loadValidationChangesForContentType,
    loading,
    validationChangesMap
  }
}

export default useValidationChanges
