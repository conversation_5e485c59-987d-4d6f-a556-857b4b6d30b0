import { request } from '@strapi/helper-plugin'

/**
 * API utility functions for validation changes
 */

/**
 * Fetch validation changes for a specific content type and locale
 * @param {string} contentType - The content-type UID
 * @param {string} locale - The target locale
 * @returns {Promise<object>} - API response with validation changes
 */
export const fetchValidationChanges = async (contentType, locale) => {
  const response = await request('/translate/validation-changes', {
    method: 'GET',
    params: {
      contentType,
      locale,
    },
  })
  return response
}

/**
 * Fetch all validation changes for a specific content type across all locales
 * @param {string} contentType - The content-type UID
 * @returns {Promise<object>} - API response with validation changes by locale
 */
export const fetchAllValidationChangesForContentType = async (contentType) => {
  const response = await request('/translate/validation-changes/content-type', {
    method: 'GET',
    params: {
      contentType,
    },
  })
  return response
}

/**
 * Clear validation changes for a specific content type and locale
 * @param {string} contentType - The content-type UID
 * @param {string} locale - The target locale
 * @returns {Promise<object>} - API response
 */
export const clearValidationChanges = async (contentType, locale) => {
  const response = await request('/translate/validation-changes/clear', {
    method: 'POST',
    body: {
      contentType,
      locale,
    },
  })
  return response
}

/**
 * Get validation changes summary for all content types
 * @returns {Promise<object>} - API response with summary
 */
export const fetchValidationChangesSummary = async () => {
  const response = await request('/translate/validation-changes/summary', {
    method: 'GET',
  })
  return response
}

/**
 * Check if validation changes exist for a content type and locale
 * @param {string} contentType - The content-type UID
 * @param {string} locale - The target locale
 * @returns {Promise<boolean>} - True if changes exist
 */
export const hasValidationChanges = async (contentType, locale) => {
  try {
    const response = await fetchValidationChanges(contentType, locale)
    return response.data && response.data.hasChanges
  } catch (error) {
    // If not found or error, assume no changes
    return false
  }
}

/**
 * Local storage utilities for validation changes
 */
const STORAGE_KEY_PREFIX = 'strapi-translate-validation-changes'

/**
 * Save validation changes to localStorage
 * @param {string} contentType - The content-type UID
 * @param {string} locale - The target locale
 * @param {object} changes - The validation changes data
 */
export const saveValidationChangesToStorage = (contentType, locale, changes) => {
  const key = `${STORAGE_KEY_PREFIX}:${contentType}:${locale}`
  try {
    localStorage.setItem(key, JSON.stringify({
      ...changes,
      savedAt: new Date().toISOString()
    }))
  } catch (error) {
    console.warn('Failed to save validation changes to localStorage:', error)
  }
}

/**
 * Load validation changes from localStorage
 * @param {string} contentType - The content-type UID
 * @param {string} locale - The target locale
 * @returns {object|null} - Saved validation changes or null
 */
export const loadValidationChangesFromStorage = (contentType, locale) => {
  const key = `${STORAGE_KEY_PREFIX}:${contentType}:${locale}`
  try {
    const stored = localStorage.getItem(key)
    return stored ? JSON.parse(stored) : null
  } catch (error) {
    console.warn('Failed to load validation changes from localStorage:', error)
    return null
  }
}

/**
 * Clear validation changes from localStorage
 * @param {string} contentType - The content-type UID
 * @param {string} locale - The target locale
 */
export const clearValidationChangesFromStorage = (contentType, locale) => {
  const key = `${STORAGE_KEY_PREFIX}:${contentType}:${locale}`
  try {
    localStorage.removeItem(key)
  } catch (error) {
    console.warn('Failed to clear validation changes from localStorage:', error)
  }
}

/**
 * Get all validation changes from localStorage for a content type
 * @param {string} contentType - The content-type UID
 * @returns {object} - Object with locale keys and their validation changes
 */
export const getAllValidationChangesFromStorage = (contentType) => {
  const result = {}
  const prefix = `${STORAGE_KEY_PREFIX}:${contentType}:`
  
  try {
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(prefix)) {
        const locale = key.substring(prefix.length)
        const stored = localStorage.getItem(key)
        if (stored) {
          result[locale] = JSON.parse(stored)
        }
      }
    }
  } catch (error) {
    console.warn('Failed to load validation changes from localStorage:', error)
  }
  
  return result
}

/**
 * Clear all validation changes from localStorage for a content type
 * @param {string} contentType - The content-type UID
 * @returns {number} - Number of entries cleared
 */
export const clearAllValidationChangesFromStorage = (contentType) => {
  const prefix = `${STORAGE_KEY_PREFIX}:${contentType}:`
  let clearedCount = 0
  
  try {
    const keysToRemove = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(prefix)) {
        keysToRemove.push(key)
      }
    }
    
    keysToRemove.forEach(key => {
      localStorage.removeItem(key)
      clearedCount++
    })
  } catch (error) {
    console.warn('Failed to clear validation changes from localStorage:', error)
  }
  
  return clearedCount
}

/**
 * Sync validation changes between server and localStorage
 * @param {string} contentType - The content-type UID
 * @param {string} locale - The target locale
 * @returns {Promise<object|null>} - Synced validation changes
 */
export const syncValidationChanges = async (contentType, locale) => {
  try {
    // Try to fetch from server first
    const serverResponse = await fetchValidationChanges(contentType, locale)
    if (serverResponse.data && serverResponse.data.hasChanges) {
      // Save to localStorage for offline access
      saveValidationChangesToStorage(contentType, locale, serverResponse.data)
      return serverResponse.data
    }
  } catch (error) {
    console.warn('Failed to fetch validation changes from server, trying localStorage:', error)
  }
  
  // Fallback to localStorage
  return loadValidationChangesFromStorage(contentType, locale)
}
