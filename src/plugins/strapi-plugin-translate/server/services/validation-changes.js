'use strict'

const _ = require('lodash')

/**
 * Service to manage validation changes data structure
 * Handles storage, retrieval, and formatting of validation changes
 * for the field validation review system
 *
 * This service enables users to review what changes were made to their content
 * during translation to meet field validation requirements (length limits, etc.)
 */
module.exports = ({ strapi }) => ({
  
  /**
   * In-memory storage for validation changes by model and locale
   * Structure: { 'contentType:locale': { changes: [], timestamp: Date, entityId: number } }
   */
  validationChangesStore: new Map(),

  /**
   * Store validation changes for a specific content type and locale
   * @param {string} contentType - The content-type UID
   * @param {string} locale - The target locale
   * @param {number} entityId - The entity ID that was translated
   * @param {array} changes - Array of validation changes
   * @returns {object} - Storage result
   */
  storeValidationChanges(contentType, locale, entityId, changes) {
    if (!contentType || !locale || !Array.isArray(changes)) {
      throw new Error('Invalid parameters for storing validation changes')
    }

    const key = `${contentType}:${locale}`
    const changeData = {
      contentType,
      locale,
      entityId,
      changes: changes.map(change => ({
        ...change,
        timestamp: new Date().toISOString()
      })),
      timestamp: new Date().toISOString(),
      totalChanges: changes.length
    }

    this.validationChangesStore.set(key, changeData)

    strapi.log.info(`Stored ${changes.length} validation changes for ${contentType}:${locale}`)
    
    return {
      success: true,
      key,
      totalChanges: changes.length
    }
  },

  /**
   * Retrieve validation changes for a specific content type and locale
   * @param {string} contentType - The content-type UID
   * @param {string} locale - The target locale
   * @returns {object|null} - Stored validation changes or null if not found
   */
  getValidationChanges(contentType, locale) {
    if (!contentType || !locale) {
      return null
    }

    const key = `${contentType}:${locale}`
    const storedData = this.validationChangesStore.get(key)

    if (!storedData) {
      return null
    }

    // Add additional metadata for frontend consumption
    return {
      ...storedData,
      hasChanges: storedData.changes.length > 0,
      changesByType: _.groupBy(storedData.changes, 'type'),
      changesByField: _.groupBy(storedData.changes, 'field')
    }
  },

  /**
   * Get all validation changes for a specific content type across all locales
   * @param {string} contentType - The content-type UID
   * @returns {object} - Object with locale keys and their validation changes
   */
  getAllValidationChangesForContentType(contentType) {
    if (!contentType) {
      return {}
    }

    const result = {}
    
    for (const [key, data] of this.validationChangesStore.entries()) {
      if (key.startsWith(`${contentType}:`)) {
        const locale = key.split(':')[1]
        result[locale] = {
          ...data,
          hasChanges: data.changes.length > 0,
          changesByType: _.groupBy(data.changes, 'type'),
          changesByField: _.groupBy(data.changes, 'field')
        }
      }
    }

    return result
  },

  /**
   * Clear validation changes for a specific content type and locale
   * @param {string} contentType - The content-type UID
   * @param {string} locale - The target locale
   * @returns {boolean} - True if cleared, false if not found
   */
  clearValidationChanges(contentType, locale) {
    if (!contentType || !locale) {
      return false
    }

    const key = `${contentType}:${locale}`
    const existed = this.validationChangesStore.has(key)
    
    if (existed) {
      this.validationChangesStore.delete(key)
      strapi.log.info(`Cleared validation changes for ${contentType}:${locale}`)
    }

    return existed
  },

  /**
   * Clear all validation changes for a specific content type
   * @param {string} contentType - The content-type UID
   * @returns {number} - Number of entries cleared
   */
  clearAllValidationChangesForContentType(contentType) {
    if (!contentType) {
      return 0
    }

    let clearedCount = 0
    const keysToDelete = []

    for (const key of this.validationChangesStore.keys()) {
      if (key.startsWith(`${contentType}:`)) {
        keysToDelete.push(key)
      }
    }

    keysToDelete.forEach(key => {
      this.validationChangesStore.delete(key)
      clearedCount++
    })

    if (clearedCount > 0) {
      strapi.log.info(`Cleared ${clearedCount} validation change entries for ${contentType}`)
    }

    return clearedCount
  },

  /**
   * Check if validation changes exist for a specific content type and locale
   * @param {string} contentType - The content-type UID
   * @param {string} locale - The target locale
   * @returns {boolean} - True if changes exist
   */
  hasValidationChanges(contentType, locale) {
    if (!contentType || !locale) {
      return false
    }

    const key = `${contentType}:${locale}`
    const data = this.validationChangesStore.get(key)
    
    return data && data.changes && data.changes.length > 0
  },

  /**
   * Get validation changes summary for all content types
   * @returns {object} - Summary of all validation changes
   */
  getValidationChangesSummary() {
    const summary = {
      totalEntries: this.validationChangesStore.size,
      contentTypes: {},
      totalChanges: 0
    }

    for (const [key, data] of this.validationChangesStore.entries()) {
      const [contentType, locale] = key.split(':')
      
      if (!summary.contentTypes[contentType]) {
        summary.contentTypes[contentType] = {
          locales: {},
          totalChanges: 0
        }
      }

      summary.contentTypes[contentType].locales[locale] = {
        changes: data.changes.length,
        timestamp: data.timestamp,
        entityId: data.entityId
      }
      
      summary.contentTypes[contentType].totalChanges += data.changes.length
      summary.totalChanges += data.changes.length
    }

    return summary
  },

  /**
   * Format validation changes for frontend display
   * @param {array} changes - Array of validation changes
   * @param {string} contentType - The content-type UID
   * @param {string} locale - The target locale
   * @returns {object} - Formatted changes for frontend
   */
  formatChangesForFrontend(changes, contentType, locale) {
    if (!Array.isArray(changes)) {
      return { formattedChanges: [], summary: {} }
    }

    const formattedChanges = changes.map(change => ({
      ...change,
      displayField: this.getFieldDisplayName(change.field, contentType),
      severity: this.getChangeSeverity(change.type),
      description: this.getChangeDescription(change)
    }))

    const summary = {
      totalChanges: changes.length,
      byType: _.countBy(changes, 'type'),
      byField: _.countBy(changes, 'field'),
      bySeverity: _.countBy(formattedChanges, 'severity')
    }

    return {
      formattedChanges,
      summary,
      contentType,
      locale
    }
  },

  /**
   * Get display name for a field path
   * @param {string} fieldPath - The field path (e.g., 'seo.metaTitle')
   * @param {string} contentType - The content-type UID
   * @returns {string} - Human-readable field name
   */
  getFieldDisplayName(fieldPath, contentType) {
    // Simple implementation - can be enhanced with schema introspection
    const parts = fieldPath.split('.')
    return parts.map(part => 
      part.replace(/([A-Z])/g, ' $1')
          .replace(/^./, str => str.toUpperCase())
          .trim()
    ).join(' → ')
  },

  /**
   * Get severity level for a change type
   * @param {string} changeType - The type of change (truncation, padding, etc.)
   * @returns {string} - Severity level (low, medium, high)
   */
  getChangeSeverity(changeType) {
    const severityMap = {
      'truncation': 'high',
      'padding': 'medium',
      'correction': 'low'
    }
    
    return severityMap[changeType] || 'medium'
  },

  /**
   * Get human-readable description for a change
   * @param {object} change - The change object
   * @returns {string} - Human-readable description
   */
  getChangeDescription(change) {
    switch (change.type) {
      case 'truncation':
        return `Content was shortened from ${change.originalLength} to ${change.newLength} characters to meet maximum length requirements.`
      case 'padding':
        return `Content was extended from ${change.originalLength} to ${change.newLength} characters to meet minimum length requirements.`
      default:
        return change.message || 'Field was modified during validation.'
    }
  }
})
