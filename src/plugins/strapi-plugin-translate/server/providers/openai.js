'use strict'

const OpenAI = require('openai');
const Bottleneck = require('bottleneck');

const isEditorJs = (text) => {
  try {
    const parsed = JSON.parse(text);
    return parsed.blocks && Array.isArray(parsed.blocks) && parsed.version;
  } catch (error) {
    return false;
  }
};

class ChatGptTranslator {
   _openAiClient;
   _options;

  constructor(_options) {
    this._options = _options;
  }

   _getOpenAiClient() {
    if (!this._openAiClient) {
      this._openAiClient = new OpenAI({
        apiKey: this._options.apiKey,
      });
    }
    return this._openAiClient;
  }

  async translate(options){
    try {
      console.log('starting new openai request', new Date().toLocaleTimeString());
      // Check if the model supports json_object response format
      const supportsJsonObject = [
        'gpt-4-1106-preview',
        'gpt-4-0125-preview', 
        'gpt-4-turbo-preview',
        'gpt-3.5-turbo-1106',
        'gpt-4o',
        'gpt-4o-mini'
      ].includes(this._options.model);

      const requestOptions = {
        model: this._options.model,
        messages: options.messages,
        max_tokens: options.maxTokens,
        temperature: 0.2,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0,
      };

      // Only add response_format for models that support it
      if (supportsJsonObject) {
        requestOptions.response_format = { type: 'json_object' };
      }

      const response = await this._getOpenAiClient().chat.completions.create(requestOptions);

      const choices = response.choices;

      // console.log('choices', choices);

      if (choices && choices.length > 0) {
        return choices[0].message?.content;
      }
      throw new Error('No result received');
    } catch (error) {
      console.trace('openai translate error:', error);

      const status = error?.response?.status;

      switch (status) {
        case 429:
          throw new Error('Too many requests');
        case 400:
          throw new Error('Bad request');
        default:
          throw new Error(`translate(): ${JSON.stringify(error)}`);
      }
    }
  }

  async usage() {
    return {
      count: 1,
      limit: 4096 * 4,
    };
  }
}

const createTranslateClient = ({ apiKey, model }) => {
  return new ChatGptTranslator({ apiKey, model });
};

class ProviderOptions {
  apiKey;
  model;
  localeMap;
  maxTokens;
  generalPrompt;

  constructor({ apiKey, model, localeMap, maxTokens, generalPrompt }) {
    if (!apiKey) throw new Error(`apiKey is not defined`);
    if (!model) throw new Error(`model is not defined`);
    this.localeMap = localeMap || {};
    this.maxTokens = maxTokens || 4096;
    this.generalPrompt = generalPrompt;

    this.apiKey = apiKey;
    this.model = model;
  }
}

module.exports = {
  provider: 'openai',
  name: 'OpenAI',

  init({ apiKey, model, localeMap, maxTokens, generalPrompt } = {}) {
    const options = new ProviderOptions({
      apiKey: apiKey || process.env.OPENAI_API_TOKEN,
      model: model || process.env.OPENAI_MODEL || 'gpt-3.5-turbo',
      maxTokens: Number(maxTokens) || Number(process.env.OPENAI_MAX_TOKENS) || 4096,
      generalPrompt: generalPrompt || process.env.OPENAI_GENERAL_PROMPT || '',
      localeMap,
    });
    const client = createTranslateClient(options);

    const limiter = new Bottleneck({
      maxConcurrent: 10,
    });

    return {
      async translate({
        text,
        priority,
        sourceLocale,
        targetLocale,
        format,
      }) {
        if (!text) {
          return '';
        }

        if (!sourceLocale) {
          throw new Error('source locale must be defined');
        }

        if (!targetLocale) {
          throw new Error('target locale must be defined');
        }

        const textArray = Array.isArray(text) ? text : [text];

        const sLocale = sourceLocale;
        const tLocale = targetLocale;

        const promptMap = {
          plain: `Translate the key content in JSON from ${sLocale} to ${tLocale}.`,
          editorjs: `Translate the key content editorjs JSON object from ${sLocale} to ${tLocale}, only translating text fields: for "paragraph" it's "text", for "header" it's "text", for "list" it's "items", for "checklist" it's "items", for "quote" it's "text" and "caption", for "table" it's "content", for "image" it's "caption", and for "link_tool" it's "meta.title" and "meta.description". It is important to preserve the structure and leave all other fields untouched.`,
        };

        if (options.generalPrompt) {
          promptMap.plain += `\n\n${options.generalPrompt}`;
          promptMap.editorjs += `\n\n${options.generalPrompt}`;
        }

        const formattedTextArray = textArray
          .map((t) => {
            try {
              if (isEditorJs(t)) {
                const editorjs = JSON.parse(t);
                return { type: 'editorjs', content: editorjs };
              } else {
                return { type: 'plain', content: t };
              }
            } catch (error) {
              return error;
            }
          })
          .filter((t) => t !== undefined);

        const messagesArray = formattedTextArray.map((t) => {
          return [
            {
              role: 'system',
              content: promptMap[t.type],
            },
            { role: 'user', content: JSON.stringify(t) },
          ];
        });

        const result = await Promise.all(
          messagesArray.map((messages) =>
            limiter.schedule(() => {
              return client.translate({
                messages,
                maxTokens: options.maxTokens,
              });
            }),
          ),
        );

        // console.log('result1', result);

        const cleanedResult = result.map((r) => {
          console.log('openai result: ', r);
          let newValue = r?.content || r || '';
          try {
            // Try to parse if it's a JSON string (e.g., '"karina"')
            const parsed = JSON.parse(newValue);
            // If it's a string, return it directly
            if (typeof parsed === 'string') {
              return parsed;
            }
            // If it's an object with type 'plain', return its content
            if (parsed && parsed.type === 'plain') {
              return parsed.content;
            }
            // If it's an object, return its content as a string
            if (parsed && parsed.content) {
              return typeof parsed.content === 'string' ? parsed.content : JSON.stringify(parsed.content);
            }
          } catch (error) {
            // If parsing fails, just return the original value (should be a string)
          }
          // Remove wrapping quotes if present (as a fallback)
          if (typeof newValue === 'string' && newValue.startsWith('"') && newValue.endsWith('"')) {
            return newValue.slice(1, -1);
          }
          return newValue;
        });

        return Array.isArray(text) ? cleanedResult : cleanedResult[0];
      },
      async usage() {
        return client.usage();
      },
    }
  }
}
