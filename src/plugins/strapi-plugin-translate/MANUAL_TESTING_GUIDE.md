# Manual Testing Guide - Validation Review System

## Overview

This guide provides step-by-step instructions for manually testing the validation review system in the Strapi translation plugin. Use this guide to demonstrate the feature and train your QA team.

## Prerequisites

- Strapi instance with the translate plugin installed
- At least two locales configured (e.g., English and Spanish)
- Content types with field validation rules
- Admin access to Strapi

## Test Setup

### 1. Create Test Content Type with Validation Rules

Create a content type that will trigger validation changes during translation:

#### Option A: Create via Strapi Admin UI

1. Go to **Content-Type Builder**
2. Create a new **Collection Type** called "Test Page"
3. Add the following fields with validation rules:

**Title Field:**
- Type: Text (Short text)
- Field name: `title`
- Advanced settings:
  - Maximum length: `60` characters
  - Required: Yes

**Description Field:**
- Type: Text (Long text)
- Field name: `description`
- Advanced settings:
  - Minimum length: `100` characters
  - Maximum length: `300` characters

**SEO Component:**
- Type: Component (repeatable: No)
- Field name: `seo`
- Create new component: `shared.seo`

**SEO Component Fields:**
- `metaTitle` (Text): Max length `60` characters
- `metaDescription` (Long text): Min length `120`, Max length `160` characters

#### Option B: Create via Schema Files

Create the following schema files:

**`api/test-page/content-types/test-page/schema.json`:**
```json
{
  "kind": "collectionType",
  "collectionName": "test_pages",
  "info": {
    "singularName": "test-page",
    "pluralName": "test-pages",
    "displayName": "Test Page"
  },
  "options": {
    "draftAndPublish": true
  },
  "pluginOptions": {
    "i18n": {
      "localized": true
    }
  },
  "attributes": {
    "title": {
      "type": "string",
      "required": true,
      "maxLength": 60
    },
    "description": {
      "type": "text",
      "minLength": 100,
      "maxLength": 300
    },
    "seo": {
      "type": "component",
      "component": "shared.seo"
    }
  }
}
```

**`components/shared/seo.json`:**
```json
{
  "collectionName": "components_shared_seos",
  "info": {
    "displayName": "SEO",
    "description": "SEO meta information"
  },
  "attributes": {
    "metaTitle": {
      "type": "string",
      "maxLength": 60
    },
    "metaDescription": {
      "type": "text",
      "minLength": 120,
      "maxLength": 160
    }
  }
}
```

### 2. Create Test Content

Create test content that will trigger validation changes:

1. Go to **Content Manager** → **Test Page**
2. Click **Create new entry**
3. Select your default locale (e.g., English)
4. Fill in the following content:

**Title:** `This is an extremely long title that definitely exceeds the 60 character limit and should be truncated during translation`

**Description:** `Short description that is below the minimum length requirement.`

**SEO Meta Title:** `This is a very long meta title that exceeds the maximum length of 60 characters and should be truncated`

**SEO Meta Description:** `Short meta description below minimum.`

5. **Save** the entry

## Test Scenarios

### Test 1: Basic Validation Review Flow

**Objective:** Verify the complete validation review workflow

**Steps:**

1. **Navigate to Translation Plugin**
   - Go to **Plugins** → **Translate**
   - Verify you see the collection table with your test content type

2. **Check Initial Button State**
   - Look at the "Test Page" row
   - Find the locale column for your target locale (e.g., Spanish)
   - Verify the "Review Changes" button (👁️ icon) is **disabled/grayed out**
   - This confirms no validation changes exist yet

3. **Start Translation**
   - Click the **Translate** button (🌍 icon) for your target locale
   - Select source locale in the dialog
   - Click **Translate** to confirm
   - Wait for translation to complete

4. **Verify Button State Change**
   - After translation completes, check the "Review Changes" button
   - It should now be **enabled** (not grayed out)
   - This indicates validation changes were captured

5. **Open Review Modal**
   - Click the **Review Changes** button (👁️ icon)
   - Verify the "Validation Changes Review" modal opens

6. **Review Modal Content**
   - **Summary Section:** Should show:
     - Total changes count
     - Changes by type (Truncation, Padding)
     - Proper counts for each type
   
   - **Detailed Changes Table:** Should show:
     - Field names (title, description, seo.metaTitle, seo.metaDescription)
     - Change types with severity badges
     - Original and new lengths
     - Human-readable descriptions

7. **Clear Changes**
   - Click **Clear Changes** button in modal
   - Confirm the action
   - Modal should close
   - "Review Changes" button should become **disabled** again

**Expected Results:**
- ✅ Button correctly disabled initially
- ✅ Button enabled after translation with validation changes
- ✅ Modal opens and displays validation changes
- ✅ Summary shows correct counts
- ✅ Table shows detailed change information
- ✅ Clear functionality works
- ✅ Button disabled after clearing

### Test 2: Different Change Types

**Objective:** Test different types of validation changes

**Test Data for Different Scenarios:**

**Truncation Only:**
- Title: `This title is way too long and exceeds the sixty character limit significantly`
- Description: `This is a very long description that meets the minimum length requirement of 100 characters but is still within the maximum limit of 300 characters so it should not be truncated during the translation process.`

**Padding Only:**
- Title: `Short title`
- Description: `Too short`

**Mixed Changes:**
- Title: `This is another very long title that will definitely be truncated`
- Description: `Short`

**Steps:**
1. Create entries with each test data scenario
2. Translate each entry
3. Review changes for each scenario
4. Verify correct change types are captured:
   - **Truncation (High Severity):** Red badge
   - **Padding (Medium Severity):** Orange badge

### Test 3: Multiple Locales

**Objective:** Test validation changes across multiple locales

**Steps:**
1. Create content in source locale
2. Translate to multiple target locales (e.g., Spanish, French)
3. Verify each locale has its own "Review Changes" button state
4. Verify changes are stored separately per locale
5. Clear changes for one locale, verify others remain

### Test 4: Edge Cases and Error Scenarios

#### Test 4.1: No Validation Changes

**Objective:** Test behavior when no validation changes occur

**Steps:**
1. Create content that meets all validation requirements:
   - Title: `Perfect Length Title` (under 60 chars)
   - Description: `This description is exactly the right length to meet the minimum requirement of 100 characters without exceeding the maximum limit.` (meets min/max requirements)
2. Translate the content
3. Verify "Review Changes" button remains **disabled**
4. Attempt to click the button (should not open modal)

#### Test 4.2: API Error Handling

**Objective:** Test error handling when API calls fail

**Steps:**
1. Disconnect from internet or block API calls
2. Try to open validation changes modal
3. Verify appropriate error message is displayed
4. Verify graceful fallback to localStorage

#### Test 4.3: Large Number of Changes

**Objective:** Test performance with many validation changes

**Steps:**
1. Create content with many fields that will trigger changes
2. Translate and verify modal performance
3. Check that all changes are displayed correctly

#### Test 4.4: Browser Refresh Persistence

**Objective:** Test localStorage persistence

**Steps:**
1. Create validation changes through translation
2. Refresh the browser
3. Verify "Review Changes" button is still enabled
4. Verify modal still shows the changes

### Test 5: User Experience Testing

#### Test 5.1: Accessibility

**Steps:**
1. Test keyboard navigation through buttons
2. Verify screen reader compatibility
3. Check color contrast for severity badges
4. Test with browser zoom at 200%

#### Test 5.2: Responsive Design

**Steps:**
1. Test on different screen sizes
2. Verify modal displays correctly on mobile
3. Check table scrolling on small screens

#### Test 5.3: Internationalization

**Steps:**
1. Change Strapi admin language
2. Verify all text is properly translated
3. Test with RTL languages if supported

## Troubleshooting Common Issues

### Issue 1: Review Changes Button Always Disabled

**Possible Causes:**
- No validation rules defined in content type schema
- Content doesn't trigger validation changes
- API connection issues

**Debug Steps:**
1. Check browser console for errors
2. Verify content type has validation rules
3. Check network tab for API calls
4. Verify translation actually completed

### Issue 2: Modal Shows "No Changes Found"

**Possible Causes:**
- Changes were cleared
- API returned empty response
- localStorage was cleared

**Debug Steps:**
1. Check localStorage in browser dev tools
2. Verify API response in network tab
3. Try translating again

### Issue 3: Changes Not Persisting

**Possible Causes:**
- localStorage disabled
- Browser privacy settings
- API storage issues

**Debug Steps:**
1. Check browser localStorage settings
2. Verify API endpoints are working
3. Check server logs for errors

## QA Team Checklist

Use this checklist for comprehensive testing:

### Functional Testing
- [ ] Button states (enabled/disabled) work correctly
- [ ] Modal opens and closes properly
- [ ] All change types are captured and displayed
- [ ] Clear changes functionality works
- [ ] Multiple locales work independently
- [ ] Persistence across browser sessions

### UI/UX Testing
- [ ] Modal is visually appealing and readable
- [ ] Severity badges are clearly distinguishable
- [ ] Table is properly formatted and scrollable
- [ ] Loading states are shown appropriately
- [ ] Error messages are user-friendly

### Performance Testing
- [ ] Modal opens quickly (< 2 seconds)
- [ ] Large numbers of changes don't cause lag
- [ ] Memory usage is reasonable
- [ ] No memory leaks after multiple uses

### Compatibility Testing
- [ ] Works in Chrome, Firefox, Safari, Edge
- [ ] Mobile responsive design
- [ ] Keyboard navigation
- [ ] Screen reader compatibility

### Error Handling
- [ ] Graceful handling of API errors
- [ ] Appropriate fallbacks when offline
- [ ] Clear error messages for users
- [ ] No console errors in normal operation

## Reporting Issues

When reporting issues, include:

1. **Steps to reproduce**
2. **Expected vs actual behavior**
3. **Browser and version**
4. **Console errors (if any)**
5. **Screenshots or screen recordings**
6. **Content type schema used**
7. **Test data that caused the issue**

### Test 6: Integration with Existing Features

#### Test 6.1: Batch Translation Integration

**Objective:** Verify validation review works with batch translation

**Steps:**
1. Create multiple entries with validation-triggering content
2. Use batch translation feature
3. Verify "Review Changes" buttons are enabled for all translated entries
4. Check that each entry has its own validation changes

#### Test 6.2: Auto-Publish Integration

**Objective:** Test validation review with auto-publish enabled

**Steps:**
1. Enable auto-publish in translation dialog
2. Translate content with validation changes
3. Verify changes are still captured even with auto-publish
4. Check that published content reflects the corrected values

### Test 7: Data Validation

#### Test 7.1: Change Data Accuracy

**Objective:** Verify captured changes match actual modifications

**Steps:**
1. Note original content values and lengths
2. Translate content
3. Compare translated content with original
4. Verify validation changes modal shows accurate:
   - Original lengths
   - New lengths
   - Field names
   - Change descriptions

#### Test 7.2: Timestamp Accuracy

**Objective:** Verify change timestamps are accurate

**Steps:**
1. Note time before translation
2. Perform translation
3. Check validation changes modal
4. Verify timestamps are recent and accurate

## Advanced Testing Scenarios

### Scenario 1: Complex Content Structure

**Test with nested components and dynamic zones:**

```json
{
  "title": "Long title that exceeds limits...",
  "content": [
    {
      "__component": "shared.text-block",
      "title": "Another long title...",
      "description": "Short desc"
    },
    {
      "__component": "shared.quote",
      "quote": "Very long quote that exceeds character limits..."
    }
  ]
}
```

### Scenario 2: Concurrent Users

**Test multiple users translating simultaneously:**

1. Have two users translate different content types
2. Verify validation changes don't interfere
3. Check that each user sees their own changes

### Scenario 3: High Volume Testing

**Test with large amounts of content:**

1. Create 50+ entries with validation-triggering content
2. Batch translate all entries
3. Verify system performance remains acceptable
4. Check memory usage doesn't grow excessively

## Success Criteria

The validation review system passes testing when:

- ✅ All functional requirements work as specified
- ✅ UI is intuitive and user-friendly
- ✅ Performance is acceptable (< 2 second load times)
- ✅ No critical bugs or console errors
- ✅ Accessibility standards are met
- ✅ Works across supported browsers and devices
- ✅ Data accuracy is maintained throughout the process
- ✅ Integration with existing features is seamless
- ✅ Handles edge cases gracefully
