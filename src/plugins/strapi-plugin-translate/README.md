# Strapi Plugin Translate

A comprehensive Strapi plugin for managing and automating content translation with advanced field validation and review capabilities.

## Features

### Core Translation Features
- **Batch Translation**: Translate multiple entries across different locales
- **Individual Translation**: Translate single entries with full control
- **Provider Support**: Extensible translation provider system
- **Usage Tracking**: Monitor API usage and costs
- **Relation Translation**: Handle complex content relationships

### Advanced Validation Review System ✨ NEW
- **Automatic Change Detection**: Captures validation changes during translation
- **Review Interface**: User-friendly modal to review all modifications
- **Change Categorization**: Groups changes by type, field, and severity
- **Persistent Storage**: Stores changes both server-side and client-side
- **Clear on Retranslate**: Automatically clears old changes when starting new translations

## Installation

```bash
npm install strapi-plugin-translate
```

## Configuration

Add the plugin to your `config/plugins.js`:

```javascript
module.exports = {
  'translate': {
    enabled: true,
    config: {
      // Your translation provider configuration
      provider: 'openai', // or your preferred provider
      // Additional configuration options
    }
  }
}
```

## Usage

### Basic Translation

1. Navigate to the plugin's main interface
2. Select your content type and locales
3. Click "Translate" to start batch translation
4. Monitor progress and review results

### Validation Review System

The plugin automatically captures and stores validation changes made during translation:

#### Reviewing Changes

1. After translation, look for the "Review Changes" button (👁️ icon)
2. Click to open the validation changes modal
3. Review the summary and detailed changes
4. See what fields were modified and why

#### Change Types

- **Truncation (High Severity)**: Content shortened to meet maximum length
- **Padding (Medium Severity)**: Content extended to meet minimum length  
- **Correction (Low Severity)**: Minor formatting or validation corrections

#### Example Change Review

```javascript
// Example of captured changes
{
  type: 'truncation',
  field: 'seo.metaTitle',
  originalLength: 85,
  newLength: 60,
  message: 'Meta title was truncated from 85 to 60 characters to meet maximum length requirement'
}
```

### API Usage

#### Programmatic Access to Validation Changes

```javascript
// Get validation changes for a content type and locale
const changes = await strapi.service('plugin::translate.validation-changes')
  .getValidationChanges('api::page.page', 'es')

// Check if changes exist
const hasChanges = await strapi.service('plugin::translate.validation-changes')
  .hasValidationChanges('api::page.page', 'es')

// Clear changes
await strapi.service('plugin::translate.validation-changes')
  .clearValidationChanges('api::page.page', 'es')
```

#### Frontend API

```javascript
import { fetchValidationChanges, clearValidationChanges } from './utils/validationChangesApi'

// Fetch changes
const response = await fetchValidationChanges(contentType, locale)

// Clear changes
await clearValidationChanges(contentType, locale)
```

## Field Validation Configuration

The system automatically detects validation rules from your content-type schemas:

```javascript
// In your content-type schema (e.g., api/page/content-types/page/schema.json)
{
  "attributes": {
    "title": {
      "type": "string",
      "maxLength": 60,  // Triggers truncation if exceeded
      "required": true
    },
    "description": {
      "type": "text",
      "minLength": 50,  // Triggers padding if below minimum
      "maxLength": 200
    },
    "seo": {
      "type": "component",
      "component": "shared.seo"
    }
  }
}
```

## Components and Services

### Backend Services

- **`validation-changes`**: Core service for managing validation changes
- **`translate`**: Main translation service with validation integration
- **`field-validation`**: Utility functions for field validation and auto-correction

### Frontend Components

- **`ValidationChangesModal`**: Modal component for reviewing changes
- **`CollectionTable`**: Enhanced table with review changes functionality
- **API utilities**: Helper functions for server communication and local storage

## API Endpoints

### Validation Changes Endpoints

- `GET /api/translate/validation-changes` - Get changes for content type and locale
- `GET /api/translate/validation-changes/content-type` - Get all changes for content type
- `POST /api/translate/validation-changes/clear` - Clear changes
- `GET /api/translate/validation-changes/summary` - Get summary of all changes

### Translation Endpoints

- `POST /api/translate/translate` - Translate single entry
- `POST /api/translate/batch-translate` - Start batch translation
- `GET /api/translate/batch-translate/content-types` - Get available content types
- `POST /api/translate/batch-update` - Update translations

## Permissions

The plugin respects Strapi's permission system:

- **Admin Authentication**: All endpoints require admin authentication
- **Content Manager Permissions**: Translation actions require appropriate content-manager permissions
- **Role-Based Access**: Configure access based on user roles

## Development

### Running Tests

```bash
npm test
```

### Integration Testing

Run the integration test script to verify the validation review system:

```bash
node test-validation-changes-integration.js
```

## Troubleshooting

### Common Issues

1. **Validation Changes Not Appearing**
   - Ensure field validation rules are defined in your schema
   - Check that auto-correction is enabled
   - Verify the translation completed successfully

2. **Review Modal Not Opening**
   - Check browser console for errors
   - Ensure proper component imports
   - Verify API routes are accessible

3. **Performance Issues**
   - Monitor memory usage for large translation jobs
   - Consider clearing old validation changes periodically
   - Check localStorage usage in browser

### Debug Mode

Enable debug logging in your Strapi configuration:

```javascript
// config/logger.js
module.exports = {
  level: 'debug'
}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This plugin is licensed under the MIT License.

## Support

For issues and questions:
- Check the [documentation](./VALIDATION_REVIEW_SYSTEM.md)
- Review existing issues
- Create a new issue with detailed information

## Changelog

### v1.4.1 - Validation Review System
- ✨ Added comprehensive validation review system
- 🔍 New review changes modal interface
- 💾 Persistent storage for validation changes
- 📊 Change categorization and severity indicators
- 🧹 Automatic cleanup on retranslation
- 📚 Comprehensive documentation and tests

### Previous Versions
- v1.4.0 - Enhanced field validation
- v1.3.x - Batch translation improvements
- v1.2.x - Provider system enhancements
- v1.1.x - Initial release
