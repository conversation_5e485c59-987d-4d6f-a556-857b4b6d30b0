import { sanitizeCountryName } from "../../../../extensions/countryServicesUrls";

const partnerLifecycles = {
  async afterCreate(event: { result: any; params: any }) {
    const { result } = event;
    await createOrUpdateUrlsForCountry(result);
  },

  async afterUpdate(event: { result: any; params: any }) {
    const { result } = event;
    await createOrUpdateUrlsForCountry(result);
  }
};

async function createOrUpdateUrlsForCountry(country: any) {
  try {
    console.log(`Processing URLs for country: ${country.name} (${country.codeAlpha2}) with locale: ${country.locale}`);

    // Define service mappings
    const serviceMappings = [
      {
        activeField: 'remittanceActive',
        pageType: 'RemittanceCountry',
        serviceKey: 'Remittance'
      },
      {
        activeField: 'mobileTopUpActive',
        pageType: 'Top-UpCountry',
        serviceKey: 'MobileTopUp'
      },
      {
        activeField: 'giftCardActive',
        pageType: 'GiftCardCountry',
        serviceKey: 'GiftCard'
      }
    ];

    // Process each service
    for (const mapping of serviceMappings) {
      if (country[mapping.activeField]) {
        await createOrUpdateUrl(country, mapping);
      } else {
        // If service is not active, remove existing URL if it exists
        // await removeUrlIfExists(country, mapping.pageType);
      }
    }
  } catch (error) {
    console.error('Error in createOrUpdateUrlsForCountry:', error);
  }
}

async function createOrUpdateUrl(country: any, mapping: any) {
  try {
    const { pageType, serviceKey } = mapping;
    const locale = country.locale || 'en';
    const urlFounds = await strapi.entityService.findMany('api::url.url', {
      filters: {
        pageType: pageType
      },
      locale,
      populate: ['country'],
    });

    const urlCountryFound = urlFounds.find((url) => url.country?.codeAlpha2 === country.codeAlpha2);
    if (urlCountryFound) {
      return;
    }

    const urlFound = urlFounds.find((url) => !!url.prefixURL);
    const countryName = sanitizeCountryName(country.name);
    const suffixURL = countryName;
    console.log(`Creating URL for ${pageType}: prefix=${urlFound.prefixURL}, suffix=${suffixURL}, locale=${locale}`);

    const urlData = {
      pageType: pageType,
      country: country.id,
      prefixURL: urlFound.prefixURL,
      suffixURL: suffixURL,
      urlType: 'Referred',
      locale,
    };
    await strapi.entityService.create('api::url.url', {
      data: urlData
    } as any);

  } catch (error) {
    console.error(`Error creating/updating URL for ${mapping.pageType}:`, error);
  }
}

async function removeUrlIfExists(country: any, pageType: string) {
  try {
    const existingUrl: any = await strapi.entityService.findMany('api::url.url', {
      filters: {
        pageType: pageType,
        country: country.id
      } as any
    });

    if (existingUrl && existingUrl.length > 0) {
      console.log(`Removing URL for ${pageType}: ${existingUrl[0].id}`);

      for (const url of existingUrl) {
        await strapi.entityService.delete('api::url.url', url.id);
      }
    }
  } catch (error) {
    console.error(`Error removing URL for ${pageType}:`, error);
  }
}

export default partnerLifecycles;
